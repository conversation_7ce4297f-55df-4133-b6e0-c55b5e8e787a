import {
  Injectable,
  Logger,
  BadRequestException,
  ConflictException,
  NotFoundException,
  InternalServerErrorException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateWalletDto } from './dto/create-wallet.dto';
import {
  WalletResponseDto,
  WalletListResponseDto,
} from './dto/wallet-response.dto';
import {
  WalletWithChain,
  WalletCreationData,
} from './interfaces/wallet.interface';
import {
  VALID_USER_STATUSES_FOR_WALLET,
  MAX_WALLETS_PER_USER,
  WALLET_ERROR_MESSAGES,
} from './constants/wallet.constants';
import { WalletValidationUtil } from './utils/wallet-validation.util';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new wallet for a user
   * @param userId - The ID of the user creating the wallet
   * @param createWalletDto - Wallet creation data
   * @returns Promise<WalletResponseDto> - Created wallet information
   * @throws BadRequestException - Invalid input data
   * @throws ConflictException - Wallet address already exists
   * @throws ForbiddenException - User not allowed to create wallets
   * @throws InternalServerErrorException - Database or system errors
   */
  async createWallet(
    userId: string,
    createWalletDto: CreateWalletDto,
  ): Promise<WalletResponseDto> {
    try {
      this.logger.log(
        `Creating wallet for user: ${userId} (length: ${userId.length})`,
      );

      // Validate user ID format
      WalletValidationUtil.validateUserId(userId);

      // Validate user exists and can create wallets
      await this.validateUserForWalletCreation(userId);

      // Validate wallet creation data with multiple chains
      const walletData: WalletCreationData = {
        userId,
        walletAddress: createWalletDto.wallet_address,
        walletType: createWalletDto.wallet_type,
        isPrimary: false, // Will be determined later
        status: 'ACTIVE',
        chains: createWalletDto.chains.map((chain) => ({
          chainId: chain.chainId,
          chainName: chain.chainName,
          symbol: chain.symbol,
        })),
      };

      // Validate multiple chains
      const chainsValidationResult =
        WalletValidationUtil.validateMultipleChains(
          walletData.chains,
          walletData.walletType,
        );
      if (!chainsValidationResult.isValid) {
        throw new BadRequestException(chainsValidationResult.errors.join(', '));
      }

      // Validate wallet creation data
      const validationResult =
        WalletValidationUtil.validateWalletCreationData(walletData);
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.errors.join(', '));
      }

      // Log warnings if any
      const allWarnings = [
        ...(validationResult.warnings || []),
        ...(chainsValidationResult.warnings || []),
      ];
      if (allWarnings.length > 0) {
        this.logger.warn(`Wallet creation warnings: ${allWarnings.join(', ')}`);
      }

      // Check if wallet address already exists
      await this.validateWalletAddressUniqueness(
        createWalletDto.wallet_address,
      );

      // Check wallet count limit
      await this.validateWalletCountLimit(userId);

      // Get existing wallets for the user to determine primary status
      const existingWallets = await this.getUserWalletsInternal(userId);

      // Determine if this should be the primary wallet
      const isPrimary = existingWallets.length === 0;
      walletData.isPrimary = isPrimary;

      // Create wallet and chains in a transaction
      const result = await this.createWalletWithChains(walletData);

      this.logger.log(
        `Successfully created wallet ${result.id} for user ${userId}`,
      );

      return result;
    } catch (error) {
      return this.handleServiceError(error, 'createWallet');
    }
  }

  /**
   * Get all wallets for a user
   * @param userId - The ID of the user
   * @returns Promise<WalletListResponseDto[]> - List of user's wallets
   */
  async getUserWallets(userId: string): Promise<WalletListResponseDto[]> {
    try {
      this.logger.log(`Fetching wallets for user: ${userId}`);

      // Validate user ID format
      WalletValidationUtil.validateUserId(userId);

      // Validate user exists
      await this.validateUserExists(userId);

      const wallets = await this.getUserWalletsInternal(userId);

      return wallets.map((wallet) => ({
        id: wallet.id,
        wallet_address: wallet.walletAddress,
        wallet_type: wallet.walletType as any, // Type assertion for Prisma enum
        is_primary: wallet.isPrimary,
        status: wallet.status,
        chain_names:
          wallet.chains.map((chain) => chain.chainName).join(', ') || 'Unknown',
        created_at: wallet.createdAt,
      }));
    } catch (error) {
      return this.handleServiceError(error, 'getUserWallets');
    }
  }

  /**
   * Internal method to get user wallets (used by other service methods)
   * @param userId - The ID of the user
   * @returns Promise<WalletWithChain[]> - List of user's wallets with chain info
   */
  private async getUserWalletsInternal(
    userId: string,
  ): Promise<WalletWithChain[]> {
    return await this.prisma.wallet.findMany({
      where: { userId },
      include: {
        chains: true, // Get all chains for each wallet
      },
      orderBy: [{ isPrimary: 'desc' }, { createdAt: 'desc' }],
    });
  }

  /**
   * Get a specific wallet by ID
   * @param walletId - The ID of the wallet
   * @param userId - The ID of the user (for authorization)
   * @returns Promise<WalletResponseDto> - Wallet details
   */
  async getWalletById(
    walletId: string,
    userId: string,
  ): Promise<WalletResponseDto> {
    try {
      this.logger.log(`Fetching wallet ${walletId} for user ${userId}`);

      // Validate IDs format
      WalletValidationUtil.validateUserId(userId);
      WalletValidationUtil.validateWalletId(walletId);

      // Validate user exists
      await this.validateUserExists(userId);

      const wallet = await this.prisma.wallet.findFirst({
        where: {
          id: walletId,
          userId: userId, // Ensure user can only access their own wallets
        },
        include: {
          chains: true, // Include all chains for this wallet
        },
      });

      if (!wallet) {
        throw new NotFoundException(WALLET_ERROR_MESSAGES.WALLET_NOT_FOUND);
      }

      if (!wallet.chains || wallet.chains.length === 0) {
        throw new InternalServerErrorException(
          WALLET_ERROR_MESSAGES.CHAIN_INFO_MISSING,
        );
      }

      return {
        id: wallet.id,
        user_id: wallet.userId,
        wallet_address: wallet.walletAddress,
        wallet_type: wallet.walletType as any, // Type assertion for Prisma enum
        is_primary: wallet.isPrimary,
        status: wallet.status,
        created_at: wallet.createdAt,
        chains: wallet.chains.map((chain) => ({
          id: chain.id,
          chain_id: chain.chainId,
          chain_name: chain.chainName,
          symbol: chain.symbol as any, // Type assertion for Prisma enum
          wallet_id: chain.walletId,
          created_at: chain.createdAt,
        })),
      };
    } catch (error) {
      return this.handleServiceError(error, 'getWalletById');
    }
  }

  /**
   * Private helper methods
   */

  /**
   * Validate that user exists in the system
   * @param userId - User ID to validate
   * @throws NotFoundException if user doesn't exist
   */
  private async validateUserExists(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, status: true },
    });

    if (!user) {
      throw new NotFoundException(WALLET_ERROR_MESSAGES.USER_NOT_FOUND);
    }
  }

  /**
   * Validate that user can create wallets (exists and has proper status)
   * @param userId - User ID to validate
   * @throws NotFoundException if user doesn't exist
   * @throws ForbiddenException if user cannot create wallets
   */
  private async validateUserForWalletCreation(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, status: true },
    });

    if (!user) {
      throw new NotFoundException(WALLET_ERROR_MESSAGES.USER_NOT_FOUND);
    }

    if (!VALID_USER_STATUSES_FOR_WALLET.includes(user.status as any)) {
      throw new ForbiddenException(WALLET_ERROR_MESSAGES.USER_NOT_ACTIVE);
    }
  }

  /**
   * Validate that wallet address is unique across the system
   * @param walletAddress - Wallet address to check for uniqueness
   * @throws ConflictException if wallet address already exists
   */
  private async validateWalletAddressUniqueness(
    walletAddress: string,
  ): Promise<void> {
    const existingWallet = await this.prisma.wallet.findFirst({
      where: { walletAddress },
      select: { id: true, userId: true },
    });

    if (existingWallet) {
      throw new ConflictException(WALLET_ERROR_MESSAGES.WALLET_ADDRESS_EXISTS);
    }
  }

  /**
   * Validate that user hasn't exceeded wallet count limit
   * @param userId - User ID to check wallet count for
   * @throws BadRequestException if wallet limit exceeded
   */
  private async validateWalletCountLimit(userId: string): Promise<void> {
    const walletCount = await this.prisma.wallet.count({
      where: { userId },
    });

    if (walletCount >= MAX_WALLETS_PER_USER) {
      throw new BadRequestException(WALLET_ERROR_MESSAGES.MAX_WALLETS_EXCEEDED);
    }
  }

  /**
   * Create wallet and multiple chains in a database transaction
   * @param walletData - Validated wallet creation data with multiple chains
   * @returns Promise<WalletResponseDto> - Created wallet with chains information
   * @throws InternalServerErrorException if transaction fails
   */
  private async createWalletWithChains(
    walletData: WalletCreationData,
  ): Promise<WalletResponseDto> {
    try {
      return await this.prisma.$transaction(async (prisma) => {
        // Create the wallet
        const wallet = await prisma.wallet.create({
          data: {
            userId: walletData.userId,
            walletAddress: walletData.walletAddress,
            walletType: walletData.walletType as any, // Type assertion for Prisma enum
            isPrimary: walletData.isPrimary,
            status: walletData.status as any, // Type assertion for Prisma enum
          },
        });

        // Create all associated chains
        const chainCreationPromises = walletData.chains.map((chainData) =>
          prisma.chain.create({
            data: {
              chainId: chainData.chainId,
              chainName: chainData.chainName,
              symbol: chainData.symbol as any, // Type assertion for Prisma enum
              walletId: wallet.id,
            },
          }),
        );

        const chains = await Promise.all(chainCreationPromises);

        return {
          id: wallet.id,
          user_id: wallet.userId,
          wallet_address: wallet.walletAddress,
          wallet_type: wallet.walletType as any, // Type assertion for Prisma enum
          is_primary: wallet.isPrimary,
          status: wallet.status,
          created_at: wallet.createdAt,
          chains: chains.map((chain) => ({
            id: chain.id,
            chain_id: chain.chainId,
            chain_name: chain.chainName,
            symbol: chain.symbol as any, // Type assertion for Prisma enum
            wallet_id: chain.walletId,
            created_at: chain.createdAt,
          })),
        };
      });
    } catch (error) {
      this.logger.error(
        `Transaction failed during wallet creation: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        WALLET_ERROR_MESSAGES.CHAIN_CREATION_FAILED,
      );
    }
  }

  /**
   * Centralized error handling for service methods
   * @param error - The error that occurred
   * @param context - The context/method where the error occurred
   * @throws Appropriate NestJS exception based on error type
   */
  private handleServiceError(error: any, context: string): never {
    // Log the error with context
    this.logger.error(
      `${context}: ${error.message}`,
      error.stack,
      WalletService.name,
    );

    // Re-throw known exceptions as-is
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof ConflictException ||
      error instanceof ForbiddenException
    ) {
      throw error;
    }

    // Handle Prisma-specific errors
    if (error.code) {
      switch (error.code) {
        case 'P2002':
          // Unique constraint violation
          const target = error.meta?.target;
          if (target?.includes('walletAddress')) {
            throw new ConflictException(
              WALLET_ERROR_MESSAGES.WALLET_ADDRESS_EXISTS,
            );
          }
          throw new ConflictException('A record with this data already exists');
        case 'P2025':
          // Record not found
          throw new NotFoundException(WALLET_ERROR_MESSAGES.WALLET_NOT_FOUND);
        case 'P2003':
          // Foreign key constraint violation
          throw new BadRequestException('Invalid reference provided');
        case 'P2014':
          // Required relation violation
          throw new BadRequestException(
            'Required relationship data is missing',
          );
        default:
          this.logger.error(
            `Unhandled Prisma error code: ${error.code}`,
            error.meta,
          );
      }
    }

    // Default to internal server error
    throw new InternalServerErrorException(
      `${WALLET_ERROR_MESSAGES.UNEXPECTED_ERROR} in ${context}`,
    );
  }
}
