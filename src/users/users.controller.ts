import {
  Controller,
  Post,
  Body,
  HttpStatus,
  HttpCode,
  UseInterceptors,
  ClassSerializerInterceptor,
  Get,
  Param,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CreateUserDto } from './dto/create-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { UsersService } from './users.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { PasswordResetResponseDto } from './dto/password-reset-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Users')
@Controller('users')
@UseInterceptors(ClassSerializerInterceptor)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register a new user',
    description:
      'Create a new user account with email, username, password, type, and business type',
  })
  @ApiResponse({ status: HttpStatus.CREATED, type: UserResponseDto })
  @ApiBody({ type: CreateUserDto, description: 'User registration data' })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'User already exists',
    schema: {
      example: {
        statusCode: 409,
        message: 'User already exists',
        error: 'Conflict',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors',
    schema: {
      example: {
        statusCode: 400,
        message: ['Email must be a valid email address'],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        statusCode: 500,
        message: 'Internal server error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User successfully registered',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        username: 'user',
        type: 'CONSUMER',
        businessType: 'RETAIL',
        status: 'PENDING',
        applicantId: '',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      },
    },
  })
  async register(
    @Body() createUserDto: CreateUserDto,
  ): Promise<UserResponseDto> {
    return this.usersService.register(createUserDto);
  }

  @Get('activate/:token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Activate user account',
    description: 'Activate user account using activation token from email',
  })
  @ApiParam({
    name: 'token',
    description: 'Activation token from email',
    example: 'abc123def456789...',
    schema: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User account activated successfully',
    schema: {
      example: {
        message: 'Account activated successfully',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Invalid or expired activation token, or account already activated',
    schema: {
      example: {
        statusCode: 400,
        message: 'Invalid or expired activation token',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error during activation',
    schema: {
      example: {
        statusCode: 500,
        message: 'Failed to activate account',
        error: 'Internal Server Error',
      },
    },
  })
  async activateAccount(@Param('token') token: string) {
    return this.usersService.activateAccount(token);
  }

  @Post('resend-activation')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend activation email',
    description: 'Resend activation email to user with a new token',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'User email address',
        },
      },
      required: ['email'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activation email sent successfully',
    schema: {
      example: {
        message: 'Activation email sent successfully',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid email format or account already activated',
    schema: {
      example: {
        statusCode: 400,
        message: 'Account is already activated',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
    schema: {
      example: {
        statusCode: 404,
        message: 'User not found',
        error: 'Not Found',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error during email sending',
    schema: {
      example: {
        statusCode: 500,
        message: 'Failed to resend activation email',
        error: 'Internal Server Error',
      },
    },
  })
  async resendActivationEmail(@Body('email') email: string) {
    return this.usersService.resendActivationEmail(email);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Request password reset',
    description: 'Send password reset email with temporary password to user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: PasswordResetResponseDto,
    description: 'Password reset email sent successfully',
  })
  @ApiBody({ type: ForgotPasswordDto, description: 'Email for password reset' })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid email format or account not active',
    schema: {
      example: {
        statusCode: 400,
        message: 'Invalid email format',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
    schema: {
      example: {
        statusCode: 404,
        message: 'User not found',
        error: 'Not Found',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        statusCode: 500,
        message: 'Internal server error',
        error: 'Internal Server Error',
      },
    },
  })
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<PasswordResetResponseDto> {
    return this.usersService.requestPasswordReset(forgotPasswordDto);
  }

  @Get('reset-password/:token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Confirm password reset',
    description: 'Confirm password reset using token from email',
  })
  @ApiParam({
    name: 'token',
    description: 'Password reset token from email',
    example: 'abc123def456789...',
    schema: {
      type: 'string',
      minLength: 1,
      maxLength: 256,
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset confirmed successfully',
    schema: {
      example: {
        message:
          'Password reset confirmed successfully. You can now login with your new password.',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid or expired reset token',
    schema: {
      example: {
        statusCode: 400,
        message: 'Invalid or expired reset token',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error during password reset confirmation',
    schema: {
      example: {
        statusCode: 500,
        message: 'Failed to confirm password reset',
        error: 'Internal Server Error',
      },
    },
  })
  async confirmPasswordReset(@Param('token') token: string) {
    return this.usersService.confirmPasswordReset(token);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get authenticated user profile with wallet information',
    description:
      'Retrieve the profile data of the currently authenticated user including their associated wallets',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: UserProfileResponseDto,
    description: 'User profile with wallet information retrieved successfully',
    schema: {
      example: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        username: 'johndoe',
        type: 'CONSUMER',
        businessType: 'RETAIL',
        status: 'ACTIVE',
        applicantId: '',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        wallets: [
          {
            id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
            wallet_address: '******************************************',
            is_primary: true,
            status: 'ACTIVE',
            chain_name: 'Ethereum Mainnet',
            created_at: '2024-01-15T10:30:00.000Z',
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Missing or invalid JWT token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Unauthorized',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User profile not found',
    schema: {
      example: {
        statusCode: 404,
        message: 'User not found',
        error: 'Not Found',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Account is not active',
    schema: {
      example: {
        statusCode: 403,
        message: 'Account is not active',
        error: 'Forbidden',
      },
    },
  })
  async getProfile(@Request() req: any): Promise<UserProfileResponseDto> {
    return this.usersService.getProfile(req.user.id, req.user.email);
  }
}
